{
  "name": "Simplified SMS Lead Processing",
  "nodes": [
    {
      "parameters": {},
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [100, 300],
      "id": "manual-trigger",
      "name": "Manual Trigger"
    },
    {
      "parameters": {
        "jsCode": "// PREPARE LEADS NODE - Clean business name and phone\nconst items = $input.all();\n\nconst preparedLeads = items.map((item, index) => {\n  const data = item.json;\n  \n  // Clean business name - remove Lithuanian characters and special chars\n  let cleanBusinessName = data.business_name || data.title || data.name || '';\n  cleanBusinessName = cleanBusinessName\n    .replace(/[ąčęėįšųūž]/gi, match => {\n      const replacements = {\n        'ą': 'a', 'Ą': 'A', 'č': 'c', 'Č': 'C', 'ę': 'e', 'Ę': 'E',\n        'ė': 'e', 'Ė': 'E', 'į': 'i', 'Į': 'I', 'š': 's', 'Š': 'S',\n        'ų': 'u', 'Ų': 'U', 'ū': 'u', 'Ū': 'U', 'ž': 'z', 'Ž': 'Z'\n      };\n      return replacements[match] || match;\n    })\n    .replace(/[,|]/g, '') // Remove commas and pipes\n    .trim();\n\n  // Clean phone number - remove all non-digits and add +370 if needed\n  let cleanPhone = '';\n  const phoneFields = [data.google_phone, data.phone, data.phoneNumber, data.owner_phone];\n  \n  for (const phoneField of phoneFields) {\n    if (phoneField && phoneField.trim()) {\n      cleanPhone = phoneField.replace(/\\D/g, '');\n      if (cleanPhone.startsWith('370')) {\n        cleanPhone = '+' + cleanPhone;\n      } else if (cleanPhone.startsWith('8') && cleanPhone.length === 9) {\n        cleanPhone = '+370' + cleanPhone.substring(1);\n      } else if (!cleanPhone.startsWith('+370') && cleanPhone.length === 8) {\n        cleanPhone = '+370' + cleanPhone;\n      }\n      if (cleanPhone.length >= 12) break; // Valid phone found\n    }\n  }\n\n  return {\n    ...data,\n    clean_business_name: cleanBusinessName,\n    clean_phone: cleanPhone,\n    row_index: index + 2 // Google Sheets is 1-indexed, +1 for header\n  };\n});\n\n// Filter out items without valid phone numbers\nconst validLeads = preparedLeads.filter(lead => lead.clean_phone && lead.clean_phone.length >= 12);\n\nreturn validLeads.map(lead => ({ json: lead }));"
      },
      "id": "prepare-leads",
      "name": "Prepare Leads",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [300, 300]
    },
    {
      "parameters": {
        "resource": "row",
        "operation": "getAll",
        "tableId": "sms_tracking",
        "returnAll": false,
        "limit": 1000,
        "select": "phone_number"
      },
      "id": "check-database",
      "name": "Check Database for Existing Phones",
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "jsCode": "// DATABASE CHECK AND SHEET MARKING\nconst leads = $('Prepare Leads').all();\nconst existingPhones = $('Check Database for Existing Phones').all();\n\n// Create set of existing phone numbers for fast lookup\nconst phoneSet = new Set(existingPhones.map(item => item.json.phone_number));\n\nconst processedLeads = leads.map(lead => {\n  const data = lead.json;\n  const phoneExists = phoneSet.has(data.clean_phone);\n  \n  return {\n    ...data,\n    sms_sent: phoneExists ? 'yes' : 'no',\n    sms_status: phoneExists ? 'already_sent' : 'unsent',\n    phone_exists_in_db: phoneExists\n  };\n});\n\nreturn processedLeads.map(lead => ({ json: lead }));"
      },
      "id": "mark-sms-status",
      "name": "Mark SMS Status in Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [700, 300]
    },
    {
      "parameters": {
        "operation": "update",
        "documentId": "YOUR_GOOGLE_SHEET_ID",
        "sheetName": "Sheet1",
        "range": "A:Z",
        "options": {
          "valueInputOption": "USER_ENTERED"
        },
        "fieldsUi": {
          "values": [
            {
              "column": "sms_sent",
              "fieldValue": "={{ $json.sms_sent }}"
            },
            {
              "column": "sms_status",
              "fieldValue": "={{ $json.sms_status }}"
            }
          ]
        },
        "locationDefine": "row",
        "rowNumber": "={{ $json.row_index }}"
      },
      "id": "update-sheet",
      "name": "Update Google Sheet Status",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4.6,
      "position": [900, 300]
    },
    {
      "parameters": {
        "jsCode": "// FILTER FOR SMS - Only leads not found in database\nconst leads = $input.all();\n\nconst unsentLeads = leads.filter(lead => {\n  return lead.json.phone_exists_in_db === false;\n});\n\nconsole.log(`Filtered ${unsentLeads.length} leads for SMS sending (out of ${leads.length} total)`);\n\nreturn unsentLeads;"
      },
      "id": "filter-for-sms",
      "name": "Filter Leads for SMS",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1100, 300]
    },
    {
      "parameters": {
        "url": "https://api.sms8.lt/send",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"username\": \"YOUR_SMS8_USERNAME\",\n  \"password\": \"YOUR_SMS8_PASSWORD\",\n  \"from\": \"YOUR_SENDER_NAME\",\n  \"to\": \"{{ $json.clean_phone }}\",\n  \"text\": \"Sveiki! Ieškome verslo partnerių. Ar domina bendradarbiavimas? Atsakykite SMS žinute. Ačiū!\"\n}",
        "options": {}
      },
      "id": "sms8-send",
      "name": "SMS8 Send SMS",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [1300, 300]
    },
    {
      "parameters": {
        "jsCode": "// PROCESS SMS RESPONSE - Update status based on SMS results\nconst smsResults = $input.all();\n\nconst processedResults = smsResults.map(result => {\n  const originalData = result.json;\n  const smsResponse = originalData.response || {};\n  \n  // Check SMS8 response format - adjust based on actual API response\n  const smsSuccess = smsResponse.status === 'success' || smsResponse.code === 200 || smsResponse.success === true;\n  \n  return {\n    ...originalData,\n    sms_status: smsSuccess ? 'success' : 'failed',\n    sms_error: smsSuccess ? null : (smsResponse.message || smsResponse.error || 'Unknown error')\n  };\n});\n\nreturn processedResults.map(result => ({ json: result }));"
      },
      "id": "process-sms-response",
      "name": "Process SMS Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1500, 300]
    }
    {
      "parameters": {
        "operation": "update",
        "documentId": "YOUR_GOOGLE_SHEET_ID",
        "sheetName": "Sheet1",
        "range": "A:Z",
        "options": {
          "valueInputOption": "USER_ENTERED"
        },
        "fieldsUi": {
          "values": [
            {
              "column": "sms_status",
              "fieldValue": "={{ $json.sms_status }}"
            }
          ]
        },
        "locationDefine": "row",
        "rowNumber": "={{ $json.row_index }}"
      },
      "id": "update-final-status",
      "name": "Update Final SMS Status",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4.6,
      "position": [1700, 300]
    },
    {
      "parameters": {
        "resource": "row",
        "operation": "create",
        "tableId": "sms_tracking",
        "columns": {
          "mappingMode": "defineBelow",
          "value": {
            "business_name": "={{ $json.clean_business_name }}",
            "phone_number": "={{ $json.clean_phone }}",
            "address": "={{ $json.address }}",
            "google_maps_url": "={{ $json.google_maps_url || $json.url }}",
            "category": "={{ $json.category }}",
            "sms_status": "={{ $json.sms_status }}",
            "phone_source": "={{ $json.phone_source }}",
            "normalized_business_name": "={{ $json.clean_business_name }}",
            "error_message": "={{ $json.sms_error }}"
          }
        }
      },
      "id": "record-to-database",
      "name": "Record Successful SMS to Database",
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [1900, 300],
      "executeOnce": false
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [
        [
          {
            "node": "Prepare Leads",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare Leads": {
      "main": [
        [
          {
            "node": "Check Database for Existing Phones",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Database for Existing Phones": {
      "main": [
        [
          {
            "node": "Mark SMS Status in Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Mark SMS Status in Data": {
      "main": [
        [
          {
            "node": "Update Google Sheet Status",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Google Sheet Status": {
      "main": [
        [
          {
            "node": "Filter Leads for SMS",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Filter Leads for SMS": {
      "main": [
        [
          {
            "node": "SMS8 Send SMS",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "SMS8 Send SMS": {
      "main": [
        [
          {
            "node": "Process SMS Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process SMS Response": {
      "main": [
        [
          {
            "node": "Update Final SMS Status",
            "type": "main",
            "index": 0
          },
          {
            "node": "Record Successful SMS to Database",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  }
}
